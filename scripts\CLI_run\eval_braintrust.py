import os
import sys
import braintrust
from dotenv import load_dotenv

# Add the project root to the Python search path to resolve module imports.
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, "../../")
sys.path.insert(0, project_root)

# Import the agent creation function.
from scripts.CLI_run.agent_cli import create_agent

# Import Braintrust evaluator - using custom evaluator since ClosedQAEvaluator is not available
from braintrust import Evaluator


load_dotenv()


# Define a custom QA evaluator function
def custom_qa_evaluator(output, expected=None, input=None):
    """
    自定義問答評估器
    評估代理回應是否包含預期的內容
    """
    if expected is None:
        return {"score": 0.5, "metadata": {"reason": "沒有預期答案進行比較"}}

    # 將輸出和預期答案轉換為小寫進行比較
    output_lower = str(output).lower()
    expected_lower = str(expected).lower()

    # 檢查是否包含關鍵詞
    if "素食" in expected_lower and "素食" in output_lower:
        return {"score": 1.0, "metadata": {"reason": "正確識別素食需求"}}
    elif "麻婆豆腐" in expected_lower and "麻婆豆腐" in output_lower:
        return {"score": 1.0, "metadata": {"reason": "正確回應麻婆豆腐查詢"}}
    elif any(keyword in output_lower for keyword in ["食譜", "製作", "步驟", "配料"]):
        return {"score": 0.8, "metadata": {"reason": "包含相關烹飪內容"}}
    else:
        return {"score": 0.2, "metadata": {"reason": "回應與預期不符"}}


# Define the evaluation dataset.
test_cases = [
    {
        "input": "請給我一個不含肉類的食譜",
        "expected": "找到的食譜應為素食，例如「香菇高麗菜飯」。",
        "metadata": {"test_case_id": "001"},
    },
    {
        "input": "如何製作麻婆豆腐？",
        "expected": "應包含麻婆豆腐的製作步驟和配料。",
        "metadata": {"test_case_id": "002"},
    },
]


# Define the evaluation task that runs your agent.
def run_agent_task(input_data):
    agent = create_agent()
    # Call agent.invoke() and extract the output from the response dictionary.
    response = agent.invoke(input_data)
    return response.get("output", str(response))


# Run the Braintrust evaluation.
project_name = "iCook-RAG-Evaluation"

# Use braintrust.init() to set up the project context.
braintrust.init(project=project_name)

# Call braintrust.Eval() using the 'evaluators' argument.
braintrust.Eval(
    name="Agent Evaluation Run",
    data=test_cases,
    task=run_agent_task,
    # Pass the custom evaluator function to the 'evaluators' list.
    evaluators=[custom_qa_evaluator],
)

print("評估任務已完成，請前往 Braintrust 儀表板查看結果。")
