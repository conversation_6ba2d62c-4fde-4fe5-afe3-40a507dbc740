import os
import sys
import braintrust
from dotenv import load_dotenv

# Add the project root to the Python search path to resolve module imports.
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, "../../")
sys.path.insert(0, project_root)

# Import the agent creation function.
from scripts.CLI_run.agent_cli import create_agent

# Import a Braintrust evaluator from the correct module.
from braintrust.evaluators import ClosedQAEvaluator


load_dotenv()

# Define the evaluation dataset.
test_cases = [
    {
        "input": "請給我一個不含肉類的食譜",
        "expected": "找到的食譜應為素食，例如「香菇高麗菜飯」。",
        "metadata": {"test_case_id": "001"},
    },
    {
        "input": "如何製作麻婆豆腐？",
        "expected": "應包含麻婆豆腐的製作步驟和配料。",
        "metadata": {"test_case_id": "002"},
    },
]


# Define the evaluation task that runs your agent.
def run_agent_task(input_data):
    agent = create_agent()
    # Call agent.invoke() and extract the output from the response dictionary.
    response = agent.invoke(input_data)
    return response.get("output", str(response))


# Run the Braintrust evaluation.
project_name = "iCook-RAG-Evaluation"

# Use braintrust.init() to set up the project context.
braintrust.init(project=project_name)

# Call braintrust.Eval() using the 'evaluators' argument.
braintrust.Eval(
    name="Agent Evaluation Run",
    data=test_cases,
    task=run_agent_task,
    # Pass the evaluator class to the 'evaluators' list.
    evaluators=[ClosedQAEvaluator],
)

print("評估任務已完成，請前往 Braintrust 儀表板查看結果。")
