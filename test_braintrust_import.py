#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("測試 braintrust 導入...")

try:
    import braintrust
    print("✓ braintrust 主模組導入成功")
    print(f"braintrust 版本: {getattr(braintrust, '__version__', '未知')}")
except ImportError as e:
    print(f"✗ braintrust 主模組導入失敗: {e}")
    exit(1)

try:
    from braintrust.evaluators import ClosedQAEvaluator
    print("✓ ClosedQAEvaluator 導入成功")
except ImportError as e:
    print(f"✗ ClosedQAEvaluator 導入失敗: {e}")
    
    # 檢查是否有其他可用的評估器
    print("\n檢查 braintrust 模組中可用的屬性:")
    attrs = [attr for attr in dir(braintrust) if not attr.startswith('_')]
    for attr in sorted(attrs):
        if 'eval' in attr.lower() or 'score' in attr.lower():
            print(f"  - {attr}")
    
    # 檢查是否有 evaluators 子模組
    try:
        import braintrust.evaluators
        print("\n✓ braintrust.evaluators 模組存在")
        print("可用的評估器:")
        for attr in dir(braintrust.evaluators):
            if not attr.startswith('_'):
                print(f"  - {attr}")
    except ImportError:
        print("\n✗ braintrust.evaluators 模組不存在")
        
        # 檢查是否需要安裝額外的套件
        print("\n可能的解決方案:")
        print("1. 檢查 braintrust 版本是否正確")
        print("2. 可能需要安裝 braintrust[evaluators] 或類似的額外依賴")
        print("3. API 可能已經改變，需要使用不同的導入路徑")
